
import { Routes, Route } from 'react-router-dom'
import './App.css'
import SignUp from './Authentification/SignUp'
import SignIn from './Authentification/SignIn'
import RhDashboard from './Rh/RhDashboard'
import Departement from './Rh/Departement'
import Encadrant from './Rh/Encadrant'
import OffreStage from './Rh/OffreStage'

function App() {

  return (
    <>
    <Routes>
      <Route path="/SignUp" element={<SignUp />} />
      <Route path="/SignIn" element={<SignIn />} />
      <Route path="/DashboardRH" element={<RhDashboard />} />
      <Route path="/Departement" element={<Departement />} />
      <Route path="/Encadrant" element={<Encadrant />} />
      <Route path="/PostInternShip" element={<OffreStage />} />
    </Routes>
  
    </>
  )
}

export default App
