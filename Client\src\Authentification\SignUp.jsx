import { Link } from "react-router-dom";
import axios from 'axios';
import { useState } from "react";
function SignUp() {

    const [formData, setFormData]= useState({
        firstname:'',
        lastname:'',
        cin:'',
        email:'',
        password:'',
        confirmPassword:'',
        phone:''
    });

    const [message, setMessage]= useState('');
    const [loading, setLoading]= useState(false);

    const handleChange = (e)=>{
        setFormData(prev => ({
            ...prev,
            [e.target.name]: e.target.value
        }));
    };

    const handleSubmit = async (e)=>{
        e.preventDefault();

        if (formData.password !== formData.confirmPassword){
            setMessage("les mots de passe ne correspondent pas");
            return;
        }
        try{
            setLoading(true);

            const res = await axios.post('http://localhost:3000/auth/register', formData);
            setMessage("Inscription réussie !");
            console.log(res.data)

            setFormData({
                firstname:'',
                lastname:'',
                cin:'',
                email:'',
                password:'',
                confirmPassword:'',
                phone:''
            });
        }catch(error){
            const msg= error.response?.data?.message || 'Erreur serveur';
            setMessage(msg);
        }finally {
            setLoading(false);
        }
        
    };


    return (
        <div className="row authentication authentication-cover-main mx-0">
            <div className="col-xxl-6 col-xl-7">
                <div className="row justify-content-center align-items-center h-100">
                    <div className="col-xxl-7 col-xl-9 col-lg-6 col-md-6 col-sm-8 col-12">
                        <div className="card custom-card my-5 border">
                            <div className="card-body p-5">
                                <p className="h5 mb-2 text-center">Sign Up</p>
                                <p className="mb-4 text-muted op-7 fw-normal text-center">Welcome! Begin by creating your account.</p>

                                <div className="row gy-3">
                                    <div className="col-xl-12">
                                        <label htmlFor="signup-firstname" className="form-label text-default">Nom<sup className="fs-12 text-danger">*</sup></label>
                                        <input type="text" className="form-control" id="signup-firstname" placeholder="Nom" />
                                    </div>
                                    <div className="col-xl-12">
                                        <label htmlFor="signup-firstname" className="form-label text-default">Prénom<sup className="fs-12 text-danger">*</sup></label>
                                        <input type="text" className="form-control" id="signup-firstname" placeholder="Prénom" />
                                    </div>
                                    <div className="col-xl-12">
                                        <label htmlFor="signup-email" className="form-label text-default">Email<sup className="fs-12 text-danger">*</sup></label>
                                        <input type="email" className="form-control" id="signup-email" placeholder="Email" />
                                    </div>
                                    <div className="col-xl-12">
                                        <label htmlFor="signup-password" className="form-label text-default">Mot de passe<sup className="fs-12 text-danger">*</sup></label>
                                        <input type="password" className="form-control" id="signup-password" placeholder="Mot de passe" />
                                    </div>
                                    <div className="col-xl-12">
                                        <label htmlFor="signup-confirmpassword" className="form-label text-default">Confirmer Mot de passe<sup className="fs-12 text-danger">*</sup></label>
                                        <input type="password" className="form-control" id="signup-confirmpassword" placeholder="Confirmer Mot de passe" />
                                        <div className="form-check mt-3">
                                            <input className="form-check-input" type="checkbox" id="defaultCheck1" />
                                            <label className="form-check-label text-muted fw-normal fs-14" htmlFor="defaultCheck1">
                                                By creating a account you agree to our
                                                <a href="#" className="text-success"><u>Terms & Conditions</u></a> and <a href="#" className="text-success"><u>Privacy Policy</u></a>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div className="d-grid mt-4">
                                    <button className="btn btn-primary">Create Account</button>
                                </div>
                                <div className="text-center">
                                    <p className="text-muted mt-3 mb-0">Déja a un compte? 
                                        <Link to="/SignIn">
                                        Sign In
                                        </Link>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-xxl-6 col-xl-5 col-lg-12 d-xl-block d-none px-0">
                <div className="authentication-cover overflow-hidden position-relative"
                     style={{
                         backgroundImage: 'url(/leoni_logo.jpg)',
                         backgroundSize: 'cover',
                         backgroundPosition: 'center',
                         backgroundRepeat: 'no-repeat',
                         minHeight: '100vh'
                     }}>                  
                </div>
            </div>
        </div>
    );
}

export default SignUp;