const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User } = require('../models');
require('dotenv').config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret_key';

exports.register = async(req,res)=>{
    const {firstname, lastname, cin, email, password, confirmPassword, phone}= req.body;
    const avatarFile = req.file;
    try{
        const existingEmail = await User.findOne({where:{email}});
        if(existingEmail){
            return res.status(400).json({message:'Email already exists'});
        }
        const existingCin = await User.findOne({where:{cin}});
        if(existingCin){
            return res.status(400).json({message:'Cin already exists'});
        }

         if (password !== confirmPassword) {
            return res.status(400).json({ message: 'Les mots de passe ne correspondent pas' });
        }

        const hashedPassword = await bcrypt.hash(password, 10);

        const avatarPath = avatarFile 
        ? `/uploads/avatars/${avatarFile.filename}` 
        : `/uploads/avatars/default-avatar.png`;

        const newUser = await User.create({
            firstname,
            lastname,
            cin,
            email,
            password:hashedPassword,
            confirmPassword,
            phone,
            avatar : avatarPath
    });
    res.status(201).json({message:'Utilisateur ajouté avec succées', user: newUser});
    }catch(error){
        console.log(error);
        res.status(500).json({message:'Internal server error'});
    }
}

exports.login = async(req,res)=>{
    const {email,password}=req.body;
    try{
        const user = await User.findOne({where:{email}});
        if(!user) return res.status(404).json({message:'Email incorrect'});

        const isMatch = await bcrypt.compare(password, user.password);
        if(!isMatch) return res.status(401).json({message:'Mot de passe incorrect'});

        const token = jwt.sign(
            {
                userId: user.userId, email: user.email, role: user.role
            },
            JWT_SECRET,
            {expiresIn:'24h'}
        );
        res.status(200).json({
            message:'connexion réussie',
            token,
            user:{
                userId: user.userId,
                firstname: user.firstname,
                lastname: user.lastname,
                email: user.email,
                role: user.role,
                avatar: user.avatar
            }
        });
    }catch(err){
        console.error(err);
        res.status(500).json({ message: 'Erreur serveur' });
    }
};

exports.logout = async (req,res)=>{
    res.status(200).json({message:'déconnexion réussie'})
};
